{"name": "chalkbox-backend", "version": "1.0.0", "description": "Backend for ChalkBox - Empowering Nepali Students to <PERSON>rn, <PERSON><PERSON>, and <PERSON><PERSON>n", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "swagger-autogen": "ts-node src/swagger.ts", "test": "jest", "prisma:generate": "prisma generate", "prisma:seed": "ts-node prisma/seed.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@govtechsg/open-attestation": "^6.9.7", "@prisma/client": "^6.9.0", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.17", "@types/jsonwebtoken": "^9.0.9", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "open-attestation": "link:@types/@govtechsg/open-attestation", "zod": "^3.22.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.0", "@types/express": "^4.17.21", "@types/node": "^20.19.0", "jest": "^29.0.0", "prisma": "^6.9.0", "ts-jest": "^29.0.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}